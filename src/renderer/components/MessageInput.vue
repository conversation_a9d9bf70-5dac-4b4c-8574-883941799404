<template>
  <div v-if="currentTheme" class="message-input" @click="handleContainerClick">
    <div class="editor-wrapper">
      <TextAreaInput ref="editorRef" :placeholder="placeholder" :disabled="disabled" :max-length="maxLength"
        @send="handleSend" @update:content="handleContentUpdate" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useMessage } from 'naive-ui'
import { computed, ref } from 'vue'
import { useNoteMessagesStore } from '../stores/noteMessages'
import { useNoteThemesStore } from '../stores/noteThemes'
import TextAreaInput from './TextAreaInput.vue'


// Props
interface Props {
  placeholder?: string
  disabled?: boolean
  maxLength?: number
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '2输入笔记内容...',
  disabled: false,
  maxLength: 5000
})

// Emits
interface Emits {
  (e: 'message-sent', content: string): void
}

const emit = defineEmits<Emits>()

const message = useMessage()
const themesStore = useNoteThemesStore()
const messagesStore = useNoteMessagesStore()


// 引用
const editorRef = ref<InstanceType<typeof TextAreaInput>>()

// 状态
const currentContent = ref('')

// 计算属性
const currentTheme = computed(() => themesStore.currentTheme)

// 处理发送消息
const handleSend = async (content: string) => {
  console.log('🚀 MessageInput.handleSend 开始执行')
  console.log('🚀 接收到的内容:', content)
  console.log('🚀 内容长度:', content?.length || 0)
  console.log('🚀 content.trim():', content?.trim())
  console.log('🚀 currentTheme.value:', currentTheme.value)
  console.log('🚀 props.disabled:', props.disabled)

  if (!content.trim() || !currentTheme.value || props.disabled) {
    console.log('❌ 发送被阻止 - content.trim():', !!content?.trim(), 'currentTheme:', !!currentTheme.value, 'disabled:', props.disabled)
    return
  }

  console.log('✅ 验证通过，开始发送消息')

  try {
    console.log('📡 准备调用 messagesStore.createMessageOnServer')
    console.log('📡 参数 - themeId:', currentTheme.value.id, 'content:', content, 'type: text')

    // 创建消息
    await messagesStore.createMessageOnServer(
      currentTheme.value.id,
      content,
      'text'
    )

    console.log('✅ 消息创建成功')

    // 清空编辑器
    console.log('🧹 准备清空编辑器')
    editorRef.value?.clearContent()
    console.log('✅ 编辑器已清空')

    // 聚焦编辑器
    console.log('🎯 准备聚焦编辑器')
    setTimeout(() => {
      editorRef.value?.focus()
      console.log('✅ 编辑器已聚焦')
    }, 100)

    // 触发事件
    console.log('📢 准备触发 message-sent 事件')
    emit('message-sent', content)
    console.log('✅ message-sent 事件已触发')

    console.log('🎉 准备显示成功消息')
    message.success('消息发送成功')
    console.log('✅ 发送流程完成')
  } catch (error) {
    console.error('❌ 发送消息失败:', error)
    console.error('❌ 错误详情:', {
      message: (error as any)?.message,
      stack: (error as any)?.stack,
      name: (error as any)?.name
    })
    message.error('发送消息失败')
  }
}

// 处理内容更新
const handleContentUpdate = (content: string) => {
  currentContent.value = content
}

// 处理容器点击事件
const handleContainerClick = (event: Event) => {
  // 如果点击的不是文本域本身，则聚焦编辑器
  const target = event.target as HTMLElement
  if (!target.closest('textarea')) {
    focus()
  }
}

// 聚焦编辑器
const focus = () => {
  editorRef.value?.focus()
}

// 获取当前内容
const getContent = () => {
  return editorRef.value?.getContent() || ''
}

// 设置内容
const setContent = (content: string) => {
  editorRef.value?.setContent(content)
}

// 清空内容
const clearContent = () => {
  editorRef.value?.clearContent()
}

// 暴露方法给父组件
defineExpose({
  focus,
  getContent,
  setContent,
  clearContent
})
</script>

<style scoped>
.message-input {
  height: 100%;
  display: flex;
  flex-direction: column;
  /* background-color: var(--n-color); */
  /* border-top: 1px solid var(--n-border-color); */
  box-sizing: border-box;
  cursor: text;
}

.editor-wrapper {
  flex: 1;
  height: 100%;
}

.input-hint {
  flex-shrink: 0;
  margin-top: 8px;
  text-align: center;
  padding: 4px 0;
}

.hint-text {
  font-size: 12px;
  color: var(--n-text-color-3);
}

.hint-text kbd {
  background-color: var(--n-color-embedded);
  border: 1px solid var(--n-border-color);
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 11px;
  font-family: inherit;
  color: var(--n-text-color-2);
  margin: 0 2px;
}
</style>
