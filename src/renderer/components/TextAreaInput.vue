<template>
  <div class="text-area-input" @click="handleContainerClick">
    <textarea ref="textareaRef" v-model="content" :placeholder="placeholder" :disabled="disabled" :maxlength="maxLength"
      class="textarea" @keydown="handleKeydown" @input="handleInput" @focus="handleFocus" @blur="handleBlur"></textarea>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

// Props
interface Props {
  placeholder?: string
  disabled?: boolean
  maxLength?: number
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '输入笔记内容...',
  disabled: false,
  maxLength: 5000
})

// Emits
interface Emits {
  (e: 'send', content: string): void
  (e: 'update:content', content: string): void
}

const emit = defineEmits<Emits>()

// 引用
const textareaRef = ref<HTMLTextAreaElement>()

// 状态
const content = ref('')
const isFocused = ref(false)

// 处理输入
const handleInput = () => {
  emit('update:content', content.value)
}

// 处理焦点
const handleFocus = () => {
  isFocused.value = true
}

const handleBlur = () => {
  isFocused.value = false
}

// 处理容器点击
const handleContainerClick = () => {
  focus()
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  console.log('🎹 检测到按键:', {
    key: event.key,
    metaKey: event.metaKey,
    ctrlKey: event.ctrlKey,
    shiftKey: event.shiftKey,
    altKey: event.altKey
  })

  if (event.key === 'Enter') {
    if (event.metaKey || event.ctrlKey || event.shiftKey || event.altKey) {
      // Cmd+Enter、Ctrl+Enter、Shift+Enter、Option+Enter 允许换行
      console.log('🔥 检测到换行快捷键')
      // 不阻止默认行为，允许换行
      return
    } else {
      // 单独的 Enter 键发送消息
      console.log('🔥 检测到 Enter 键，准备发送消息')
      event.preventDefault()
      event.stopPropagation()
      handleSend()
    }
  }
}

// 处理发送
const handleSend = () => {
  console.log('📤 TextAreaInput.handleSend 开始执行')
  console.log('📤 content.value:', content.value)
  console.log('📤 content.trim():', content.value.trim())
  console.log('📤 props.disabled:', props.disabled)

  if (!content.value.trim() || props.disabled) {
    console.log('❌ 发送被阻止 - content.trim():', !!content.value.trim(), 'disabled:', props.disabled)
    return
  }

  console.log('📤 准备触发 send 事件')
  emit('send', content.value.trim())
  console.log('✅ send 事件已触发')
}



// 获取内容
const getContent = (): string => {
  return content.value
}

// 设置内容
const setContent = (newContent: string) => {
  content.value = newContent
}

// 清空内容
const clearContent = () => {
  content.value = ''
}

// 聚焦
const focus = () => {
  if (textareaRef.value) {
    textareaRef.value.focus()
  }
}

// 监听禁用状态
watch(() => props.disabled, (disabled) => {
  if (disabled && textareaRef.value) {
    textareaRef.value.blur()
  }
})

// 生命周期
onMounted(() => {
  // 组件挂载后聚焦
  focus()
})

// 暴露方法给父组件
defineExpose({
  getContent,
  setContent,
  clearContent,
  focus
})
</script>

<style scoped>
.text-area-input {
  width: 100%;
  height: 100%;
  cursor: text;
  display: flex;
  flex-direction: column;

  box-sizing: border-box;
}

.textarea {
  width: 100%;
  height: 90%;
  min-height: 60px;

  color: var(--n-text-color);
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  outline: none;
  transition: border-color 0.2s ease;
  font-family: inherit;
  box-sizing: border-box;
}


.textarea:disabled {
  cursor: not-allowed;
}


/* 滚动条样式 */
.textarea::-webkit-scrollbar {
  width: 6px;
}

.textarea::-webkit-scrollbar-track {
  background: transparent;
}
</style>
